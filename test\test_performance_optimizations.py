"""
Test suite for performance optimizations.
This test ensures that the performance improvements are working correctly.
"""

import pytest
import time
import gc
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, List

# Import the modules we need to test
from config_loader import ConfigLoader
from market_type_scanner import OptionsScanner, MarketData
from fyers_connect import FyersConnect


class TestPerformanceOptimizations:
    """Test class for performance optimizations."""

    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=ConfigLoader)
        config.pivot_point_enabled = True
        config.min_delta = 0.27
        config.max_delta = 0.64
        config.symbols = ['NIFTY']
        config.env_path = '../.env'
        config.get_symbol_config.return_value = {"num_strikes_each_side": 10}
        config.get_csv_file_for_market_type.return_value = 'NSE_FO.csv'
        return config

    @pytest.fixture
    def mock_fyers_config(self):
        """Create a mock Fyers configuration."""
        fyers_config = Mock()
        fyers_config.config = {'client_id': 'test_client'}
        return fyers_config

    def test_symbol_parsing_cache_performance(self, mock_config):
        """Test that symbol parsing cache improves performance."""
        scanner = OptionsScanner(mock_config)
        
        # Mock the symbol parser
        mock_symbol_parser = Mock()
        mock_parsed_symbol = Mock()
        mock_parsed_symbol.underlying = 'NIFTY'
        mock_parsed_symbol.strike_price = 24000
        mock_parsed_symbol.option_type = 'CE'
        mock_symbol_parser.parse_symbol.return_value = mock_parsed_symbol
        scanner.symbol_parser = mock_symbol_parser
        
        # Create test market data
        test_symbols = [f'NSE:NIFTY25JUL{24000 + i * 50}CE' for i in range(100)]
        market_data = {}
        for symbol in test_symbols:
            market_data[symbol] = MarketData(
                symbol=symbol,
                ltp=100.0,
                volume=10000
            )
        
        # First call - should populate cache
        start_time = time.time()
        filtered_symbols_1 = scanner.convert_to_filtered_symbols(market_data)
        first_call_time = time.time() - start_time
        
        # Second call - should use cache
        start_time = time.time()
        filtered_symbols_2 = scanner.convert_to_filtered_symbols(market_data)
        second_call_time = time.time() - start_time
        
        # Verify results are the same
        assert len(filtered_symbols_1) == len(filtered_symbols_2)
        
        # Second call should be faster (cache hit)
        assert second_call_time < first_call_time
        
        # Verify cache was used (parser should be called only once per symbol)
        assert mock_symbol_parser.parse_symbol.call_count == len(test_symbols)

    def test_fyers_connect_caching(self, mock_fyers_config):
        """Test that FyersConnect caching works correctly."""
        fyers_connect = FyersConnect(mock_fyers_config)
        
        # Test spot price caching
        test_symbol = 'NIFTY'
        test_price = 24000.0
        
        # Mock the internal method
        with patch.object(fyers_connect, '_get_quote_data') as mock_get_quote:
            mock_get_quote.return_value = {'lp': test_price}
            
            # First call - should fetch from API
            price1 = fyers_connect.get_spot_price(test_symbol)
            
            # Second call - should use cache
            price2 = fyers_connect.get_spot_price(test_symbol)
            
            # Verify prices are the same
            assert price1 == price2 == test_price
            
            # Verify API was called only once
            assert mock_get_quote.call_count == 1

    def test_option_chain_caching(self, mock_fyers_config):
        """Test that option chain caching works correctly."""
        fyers_connect = FyersConnect(mock_fyers_config)
        
        # Mock the fyers API
        mock_fyers = Mock()
        fyers_connect.fyers = mock_fyers
        
        test_symbol = 'NIFTY'
        test_option_chain = [
            {
                'strike': 24000,
                'expiry_date': '2025-07-31',
                'call_symbol': 'NSE:NIFTY25JUL24000CE',
                'put_symbol': 'NSE:NIFTY25JUL24000PE',
                'call_delta': 0.45,
                'put_delta': -0.55
            }
        ]
        
        # Mock the API response
        mock_response = {
            'code': 200,
            'd': {
                'optionsChain': [
                    {
                        'symbol': 'NSE:NIFTY25JUL24000CE',
                        'v': {'lp': 150.0, 'previousClose': 148.0}
                    },
                    {
                        'symbol': 'NSE:NIFTY25JUL24000PE',
                        'v': {'lp': 50.0, 'previousClose': 51.0}
                    }
                ]
            }
        }
        
        with patch.object(fyers_connect, '_get_quote_data') as mock_get_quote:
            mock_get_quote.return_value = {'lp': 24100.0}  # Spot price
            
            with patch.object(mock_fyers, 'optionchain') as mock_option_chain:
                mock_option_chain.return_value = mock_response
                
                # First call - should fetch from API
                chain1 = fyers_connect.get_option_chain(test_symbol, 'MONTHLY', 10)
                
                # Second call - should use cache
                chain2 = fyers_connect.get_option_chain(test_symbol, 'MONTHLY', 10)
                
                # Verify results are the same
                assert chain1 == chain2
                
                # Verify API was called only once
                assert mock_option_chain.call_count == 1

    def test_memory_optimization_large_dataset(self, mock_config):
        """Test memory optimization for large datasets."""
        scanner = OptionsScanner(mock_config)
        
        # Create a large dataset
        large_market_data = {}
        for i in range(10000):
            symbol = f'NSE:TEST{i}25JUL24000CE'
            large_market_data[symbol] = MarketData(
                symbol=symbol,
                ltp=100.0 + i,
                volume=10000 + i,
                open_price=99.0 + i,
                high_price=102.0 + i,
                low_price=98.0 + i,
                close_price=100.5 + i,
                prev_close=99.5 + i,
                change=1.0,
                change_percent=1.0
            )
        
        # Measure memory before optimization
        gc.collect()
        
        # Apply memory optimization
        optimized_data = scanner.optimize_memory_usage(large_market_data)
        
        # Verify data integrity
        assert len(optimized_data) == len(large_market_data)
        
        # Verify essential fields are preserved
        for symbol, data in optimized_data.items():
            assert hasattr(data, 'ltp')
            assert hasattr(data, 'volume')
            assert hasattr(data, 'symbol')
            assert data.symbol == symbol

    def test_cache_cleanup_functionality(self, mock_fyers_config):
        """Test that cache cleanup works correctly."""
        fyers_connect = FyersConnect(mock_fyers_config)
        
        # Add some test data to caches
        fyers_connect._spot_price_cache['TEST1'] = 100.0
        fyers_connect._cache_timestamp['TEST1'] = time.time() - 400  # Expired
        
        fyers_connect._spot_price_cache['TEST2'] = 200.0
        fyers_connect._cache_timestamp['TEST2'] = time.time()  # Fresh
        
        # Run cleanup
        fyers_connect._cleanup_expired_cache()
        
        # Verify expired entries are removed
        assert 'TEST1' not in fyers_connect._spot_price_cache
        assert 'TEST1' not in fyers_connect._cache_timestamp
        
        # Verify fresh entries are kept
        assert 'TEST2' in fyers_connect._spot_price_cache
        assert 'TEST2' in fyers_connect._cache_timestamp

    def test_batch_processing_optimization(self, mock_config):
        """Test that batch processing optimization works."""
        from fyers_client import FyersClient
        
        # Mock the FyersClient
        with patch('fyers_client.FyersClient') as MockFyersClient:
            mock_client = Mock()
            MockFyersClient.return_value = mock_client
            
            # Mock the get_quotes method to simulate batch processing
            def mock_get_quotes(symbols):
                return {symbol: MarketData(symbol=symbol, ltp=100.0, volume=1000) 
                       for symbol in symbols}
            
            mock_client.get_quotes.side_effect = mock_get_quotes
            mock_client.authenticate.return_value = True
            
            # Create a large symbol list
            large_symbol_list = [f'NSE:TEST{i}-EQ' for i in range(5000)]
            
            # Test optimized processing
            start_time = time.time()
            result = mock_client.get_quotes_optimized(large_symbol_list, chunk_size=1000)
            processing_time = time.time() - start_time
            
            # Verify all symbols were processed
            assert len(result) == len(large_symbol_list)
            
            # Verify processing completed in reasonable time
            assert processing_time < 60  # Should complete within 60 seconds

    def test_performance_regression_prevention(self, mock_config):
        """Test to prevent performance regressions."""
        scanner = OptionsScanner(mock_config)
        
        # Create test data
        test_data = {}
        for i in range(1000):
            symbol = f'NSE:NIFTY25JUL{24000 + i}CE'
            test_data[symbol] = MarketData(
                symbol=symbol,
                ltp=100.0,
                volume=10000
            )
        
        # Mock symbol parser
        mock_symbol_parser = Mock()
        mock_parsed_symbol = Mock()
        mock_parsed_symbol.underlying = 'NIFTY'
        mock_symbol_parser.parse_symbol.return_value = mock_parsed_symbol
        scanner.symbol_parser = mock_symbol_parser
        
        # Measure performance
        start_time = time.time()
        result = scanner.convert_to_filtered_symbols(test_data)
        processing_time = time.time() - start_time
        
        # Performance benchmark: should process 1000 symbols in under 5 seconds
        assert processing_time < 5.0
        assert len(result) == len(test_data)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
